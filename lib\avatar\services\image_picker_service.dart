import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:logger/logger.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/image_config.dart';

/// Service for handling image picking with proper permissions and validation
class ImagePickerService {
  static final Logger _logger = Logger();
  static const String _logTag = '[ImagePickerService]';
  static final ImagePicker _picker = ImagePicker();

  /// Pick image from gallery with configuration
  static Future<File?> pickImageFromGallery({
    required ImageConfig config,
    required BuildContext context,
  }) async {
    try {
      // Request permissions
      if (!await _requestGalleryPermission()) {
        _showPermissionDeniedDialog(context, 'Gallery');
        return null;
      }

      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: config.maxWidth.toDouble(),
        maxHeight: config.maxHeight.toDouble(),
        imageQuality: config.imageQuality,
        requestFullMetadata: false, // For better performance
      );

      if (image != null) {
        final file = File(image.path);

        // Validate the picked image
        final validationResult = await _validateImage(file, config);
        if (validationResult != null) {
          _showErrorSnackBar(context, validationResult);
          return null;
        }

        _logger.i('$_logTag ✅ Image picked from gallery: ${image.path}');
        return file;
      }

      return null;
    } catch (e) {
      _logger.e('$_logTag ❌ Failed to pick image from gallery: $e');
      _showErrorSnackBar(context, 'Failed to pick image from gallery');
      return null;
    }
  }

  /// Pick image from camera with configuration
  static Future<File?> pickImageFromCamera({
    required ImageConfig config,
    required BuildContext context,
  }) async {
    try {
      // Request permissions
      if (!await _requestCameraPermission()) {
        _showPermissionDeniedDialog(context, 'Camera');
        return null;
      }

      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: config.maxWidth.toDouble(),
        maxHeight: config.maxHeight.toDouble(),
        imageQuality: config.imageQuality,
        requestFullMetadata: false, // For better performance
      );

      if (image != null) {
        final file = File(image.path);

        // Validate the picked image
        final validationResult = await _validateImage(file, config);
        if (validationResult != null) {
          _showErrorSnackBar(context, validationResult);
          return null;
        }

        _logger.i('$_logTag ✅ Image captured from camera: ${image.path}');
        return file;
      }

      return null;
    } catch (e) {
      _logger.e('$_logTag ❌ Failed to capture image from camera: $e');
      _showErrorSnackBar(context, 'Failed to capture image from camera');
      return null;
    }
  }

  /// Show image source selection dialog
  static Future<File?> showImageSourceDialog({
    required BuildContext context,
    required ImageConfig config,
    String title = 'Select Image Source',
  }) async {
    return showModalBottomSheet<File?>(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(20),
              ),
            ),
            child: SafeArea(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Handle bar
                  Container(
                    width: 40,
                    height: 4,
                    margin: const EdgeInsets.symmetric(vertical: 12),
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),

                  // Title
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 8,
                    ),
                    child: Text(
                      title,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Camera option
                  ListTile(
                    leading: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.blue.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(Icons.camera_alt, color: Colors.blue),
                    ),
                    title: const Text('Camera'),
                    subtitle: const Text('Take a new photo'),
                    onTap: () async {
                      Navigator.pop(context);
                      final file = await pickImageFromCamera(
                        config: config,
                        context: context,
                      );
                      if (context.mounted) {
                        Navigator.pop(context, file);
                      }
                    },
                  ),

                  // Gallery option
                  ListTile(
                    leading: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.photo_library,
                        color: Colors.green,
                      ),
                    ),
                    title: const Text('Gallery'),
                    subtitle: const Text('Choose from gallery'),
                    onTap: () async {
                      Navigator.pop(context);
                      final file = await pickImageFromGallery(
                        config: config,
                        context: context,
                      );
                      if (context.mounted) {
                        Navigator.pop(context, file);
                      }
                    },
                  ),

                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),
    );
  }

  /// Request gallery permission
  static Future<bool> _requestGalleryPermission() async {
    if (Platform.isAndroid) {
      final status = await Permission.photos.request();
      return status.isGranted;
    } else if (Platform.isIOS) {
      final status = await Permission.photos.request();
      return status.isGranted;
    }
    return true; // For other platforms
  }

  /// Request camera permission
  static Future<bool> _requestCameraPermission() async {
    final status = await Permission.camera.request();
    return status.isGranted;
  }

  /// Validate picked image
  static Future<String?> _validateImage(
    File imageFile,
    ImageConfig config,
  ) async {
    try {
      // Check if file exists
      if (!await imageFile.exists()) {
        return 'Image file does not exist';
      }

      // Check file size
      final fileSize = await imageFile.length();
      if (fileSize > config.maxFileSizeBytes) {
        final maxSizeMB = (config.maxFileSizeBytes / (1024 * 1024))
            .toStringAsFixed(1);
        return 'Image must be less than ${maxSizeMB}MB';
      }

      // Check file extension
      final extension = imageFile.path.split('.').last.toLowerCase();
      final allowedExtensions =
          config.allowedExtensions.map((e) => e.replaceAll('.', '')).toList();
      if (!allowedExtensions.contains(extension)) {
        return 'Invalid format. Allowed: ${allowedExtensions.join(', ')}';
      }

      return null; // Valid image
    } catch (e) {
      _logger.e('$_logTag ❌ Image validation error: $e');
      return 'Failed to validate image';
    }
  }

  /// Show permission denied dialog
  static void _showPermissionDeniedDialog(
    BuildContext context,
    String permission,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('$permission Permission Required'),
            content: Text('Please grant $permission permission to continue.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  openAppSettings();
                },
                child: const Text('Settings'),
              ),
            ],
          ),
    );
  }

  /// Show error snackbar
  static void _showErrorSnackBar(BuildContext context, String message) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        duration: const Duration(milliseconds: 3000),
      ),
    );
  }
}
