import 'dart:io';
import 'dart:typed_data';
import 'package:logger/logger.dart';
import 'package:path/path.dart' as path;
import '../models/auth/auth_result.dart';
import '../avatar/services/avatar_service.dart';
import 'supabase_service.dart';

/// Service for handling file uploads to Supabase Storage
class StorageService extends BaseSupabaseService {
  static final Logger _logger = Logger();
  static const String _logTag = '[StorageService]';

  // Storage bucket names - matching your actual Supabase buckets
  static const String _profileImagesBucket = 'profile-images';
  static const String _backgroundImagesBucket =
      'backgroung images'; // Matches your actual bucket name
  static const String _productImagesBucket = 'product-images';

  /// Upload profile image - delegates to AvatarService
  static Future<AuthResult<String>> uploadProfileImage({
    required String userId,
    required File imageFile,
  }) async {
    return AvatarService.uploadProfileImage(
      userId: userId,
      imageFile: imageFile,
    );
  }

  /// Upload background image - delegates to AvatarService
  static Future<AuthResult<String>> uploadBackgroundImage({
    required String userId,
    required File imageFile,
  }) async {
    return AvatarService.uploadBackgroundImage(
      userId: userId,
      imageFile: imageFile,
    );
  }

  /// Upload product image
  static Future<AuthResult<String>> uploadProductImage({
    required String userId,
    required File imageFile,
    String? productId,
  }) async {
    return _uploadImage(
      bucket: _productImagesBucket,
      userId: userId,
      imageFile: imageFile,
      prefix: productId != null ? 'product_$productId' : 'product',
    );
  }

  /// Generic image upload method
  static Future<AuthResult<String>> _uploadImage({
    required String bucket,
    required String userId,
    required File imageFile,
    required String prefix,
  }) async {
    try {
      _logger.i('$_logTag Uploading image to bucket: $bucket');

      // Validate file exists
      if (!await imageFile.exists()) {
        const error = 'Image file does not exist';
        _logger.e('$_logTag ❌ $error');
        return AuthResult.error(error);
      }

      // Check file size (max 10MB)
      final fileSize = await imageFile.length();
      if (fileSize > 10 * 1024 * 1024) {
        const error = 'Image file must be less than 10MB';
        _logger.e('$_logTag ❌ $error');
        return AuthResult.error(error);
      }

      // Get file extension
      final fileExtension = path.extension(imageFile.path).toLowerCase();
      if (!_isValidImageExtension(fileExtension)) {
        const error = 'Invalid image format. Please use JPG, PNG, or WebP';
        _logger.e('$_logTag ❌ $error');
        return AuthResult.error(error);
      }

      // Generate unique filename
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = '${prefix}_${userId}_$timestamp$fileExtension';

      // Read file bytes
      final Uint8List fileBytes = await imageFile.readAsBytes();

      // Upload to Supabase Storage
      await BaseSupabaseService.client.storage
          .from(bucket)
          .uploadBinary(fileName, fileBytes);

      // Get public URL
      final publicUrl = BaseSupabaseService.client.storage
          .from(bucket)
          .getPublicUrl(fileName);

      _logger.i('$_logTag ✅ Image uploaded successfully: $publicUrl');
      return AuthResult.success(publicUrl);
    } catch (e) {
      final error = 'Failed to upload image: ${e.toString()}';
      _logger.e('$_logTag ❌ $error');
      return AuthResult.error(error);
    }
  }

  /// Delete image from storage
  static Future<AuthResult<bool>> deleteImage({
    required String bucket,
    required String fileName,
  }) async {
    try {
      _logger.i('$_logTag Deleting image: $fileName from bucket: $bucket');

      await BaseSupabaseService.client.storage.from(bucket).remove([fileName]);

      _logger.i('$_logTag ✅ Image deleted successfully');
      return AuthResult.success(true);
    } catch (e) {
      final error = 'Failed to delete image: ${e.toString()}';
      _logger.e('$_logTag ❌ $error');
      return AuthResult.error(error);
    }
  }

  /// Extract filename from URL for deletion
  static String? extractFileNameFromUrl(String url, String bucket) {
    try {
      final uri = Uri.parse(url);
      final pathSegments = uri.pathSegments;

      // Find bucket in path and get the filename after it
      final bucketIndex = pathSegments.indexOf(bucket);
      if (bucketIndex != -1 && bucketIndex < pathSegments.length - 1) {
        return pathSegments[bucketIndex + 1];
      }

      return null;
    } catch (e) {
      _logger.e('$_logTag ❌ Failed to extract filename from URL: $e');
      return null;
    }
  }

  /// Delete profile image
  static Future<AuthResult<bool>> deleteProfileImage(String imageUrl) async {
    final fileName = extractFileNameFromUrl(imageUrl, _profileImagesBucket);
    if (fileName == null) {
      return AuthResult.error('Invalid image URL');
    }

    return deleteImage(bucket: _profileImagesBucket, fileName: fileName);
  }

  /// Delete background image
  static Future<AuthResult<bool>> deleteBackgroundImage(String imageUrl) async {
    final fileName = extractFileNameFromUrl(imageUrl, _backgroundImagesBucket);
    if (fileName == null) {
      return AuthResult.error('Invalid image URL');
    }

    return deleteImage(bucket: _backgroundImagesBucket, fileName: fileName);
  }

  /// Validate image file extension
  static bool _isValidImageExtension(String extension) {
    const validExtensions = ['.jpg', '.jpeg', '.png', '.webp'];
    return validExtensions.contains(extension);
  }

  /// Get storage bucket names for external use
  static String get profileImagesBucket => _profileImagesBucket;
  static String get backgroundImagesBucket => _backgroundImagesBucket;
  static String get productImagesBucket => _productImagesBucket;
}
